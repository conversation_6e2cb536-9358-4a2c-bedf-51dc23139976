========================================
AIStudio Real-time Log: hunyuan3d
Started: 2025-09-24T19:47:05.424Z
File: process_hunyuan3d_2025-09-24_14-47-05_001.log
========================================

[2025-09-24T19:47:05.424Z] [STDOUT] O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.
[2025-09-24T19:47:05.427Z] [STDOUT] "O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe"
"O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\get-pip.py"
[2025-09-24T19:47:08.526Z] [STDOUT] Creating virtual environment using portable Python...
[2025-09-24T19:47:08.526Z] [STDOUT] "O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe" -m virtualenv "O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv"
[2025-09-24T19:47:19.022Z] [STDOUT] sitecustomize.py applied
created virtual environment CPython3.11.9.final.0-64 in 9250ms
  creator CPython3Windows(dest=O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, setuptools=bundle, via=copy, app_data_dir=C:\Users\<USER>\AppData\Local\pypa\virtualenv)
    added seed packages: Deprecated==1.2.18, MarkupSafe==2.1.5, PyMatting==1.1.13, PyYAML==6.0.2, accelerate==1.5.2, aiofiles==23.2.1, annotated_types==0.7.0, antlr4_python3_runtime==4.9.3, anyio==4.9.0, attrs==25.3.0, certifi==2022.12.7, charset_normalizer==2.1.1, click==8.1.8, colorama==0.4.6, coloredlogs==15.0.1, contourpy==1.3.1, custom_rasterizer==0.1, cycler==0.12.1, dataclasses_json==0.6.7, diffusers==0.32.2, einops==0.8.1, fastapi==0.115.6, ffmpy==0.5.0, filelock==3.13.1, flatbuffers==25.2.10, fonttools==4.56.0, fsspec==2024.6.1, gradio==4.44.1, gradio_client==1.3.0, gradio_litmodel3d==0.0.1, groovy==0.1.2, h11==0.14.0, httpcore==1.0.7, httpx==0.28.1, huggingface_hub==0.29.3, humanfriendly==10.0, idna==3.4, imageio==2.37.0, importlib_metadata==8.6.1, importlib_resources==6.5.2, jinja2==3.1.4, jsonschema==4.23.0, jsonschema_specifications==2024.10.1, kiwisolver==1.4.8, lazy_loader==0.4, llvmlite==0.44.0, markdown_it_py==3.0.0, marshmallow==3.26.1, matplotlib==3.10.1, mdurl==0.1.2, mesh_processor==0.1.0, mpmath==1.3.0, msvc_runtime==14.42.34433, mypy_extensions==1.0.0, networkx==3.3, ninja==********, numba==0.61.0, numpy==1.25.2, omegaconf==2.3.0, onnxruntime==1.21.0, opencv_python==*********, opencv_python_headless==*********, orjson==3.10.16, packaging==24.2, pandas==2.2.3, pillow==10.4.0, pip==25.2, platformdirs==4.3.7, pooch==1.8.2, protobuf==6.30.2, psutil==7.0.0, pybind11==2.13.6, pydantic==2.10.5, pydantic_core==2.27.2, pydub==0.25.1, pygltflib==1.16.3, pygments==2.19.1, pymeshlab==2023.12.post3, pyparsing==3.2.3, pyreadline3==3.5.4, python_dateutil==2.9.0.post0, python_multipart==0.0.20, pytz==2025.2, referencing==0.36.2, regex==2024.11.6, rembg==2.0.65, requests==2.32.3, rich==13.9.4, rpds_py==0.24.0, ruff==0.11.2, safehttpx==0.1.6, safetensors==0.5.3, scikit_image==0.25.2, scipy==1.15.2, semantic_version==2.10.0, setuptools==80.9.0, shellingham==1.5.4, six==1.17.0, sniffio==1.3.1, starlette==0.41.3, sympy==1.13.1, tifffile==2025.3.13, tokenizers==0.21.1, tomlkit==0.12.0, torch==2.5.1+cu124, torchaudio==2.5.1+cu124, torchvision==0.20.1+cu124, tqdm==4.67.1, transformers==4.50.3, trimesh==4.6.5, typer==0.15.2, typing_extensions==4.12.2, typing_inspect==0.9.0, typing_inspection==0.4.0, tzdata==2025.2, urllib3==2.3.0, uvicorn==0.34.0, websockets==12.0, wrapt==1.17.2, xatlas==0.0.9, zipp==3.21.0
  activators BashActivator,BatchActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator
[2025-09-24T19:47:19.079Z] [STDOUT] 1 file(s) copied.
[2025-09-24T19:47:19.384Z] [STDOUT] Portable Python located at: O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe
[2025-09-24T19:47:19.385Z] [STDOUT] Virtual environment Python set to: "O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-09-24T19:47:19.388Z] [STDOUT] _
[2025-09-24T19:47:19.389Z] [STDOUT] Current Python: "O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-09-24T19:47:19.390Z] [STDOUT] Virtual Env: O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-09-24T19:47:19.391Z] [STDOUT] _
[2025-09-24T19:47:19.398Z] [STDOUT] Current Python: "O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-09-24T19:47:19.398Z] [STDOUT] Virtual Env: O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-09-24T19:47:19.399Z] [STDOUT] Starting the server, please wait...
[2025-09-24T19:48:34.962Z] [STDERR] O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Lib\site-packages\transformers\utils\hub.py:105: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
[2025-09-24T19:49:06.511Z] [STDOUT] Loading example img list ...
Loading example txt list ...
Loading example mv list ...
[2025-09-24T19:49:06.512Z] [STDERR] Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<?, ?it/s]
[2025-09-24T19:49:06.656Z] [STDERR] Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[2025-09-24T19:49:06.658Z] [STDERR] Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 16150.21it/s]
[2025-09-24T19:49:07.177Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-09-24T19:49:24.781Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:17<01:28, 17.60s/it]
[2025-09-24T19:49:24.927Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:17<00:13,  4.62s/it]
[2025-09-24T19:49:25.209Z] [STDERR] Loading pipeline components...:  83%|########3 | 5/6 [00:18<00:02,  2.32s/it]
[2025-09-24T19:49:25.625Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:18<00:00,  1.81s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:18<00:00,  3.07s/it]
[2025-09-24T19:49:32.188Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-09-24T19:50:04.098Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:31<02:39, 31.91s/it]
[2025-09-24T19:50:04.315Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:32<00:25,  8.35s/it]
[2025-09-24T19:50:04.706Z] [STDERR] Loading pipeline components...:  83%|########3 | 5/6 [00:32<00:04,  4.16s/it]
[2025-09-24T19:50:13.070Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:40<00:00,  5.29s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:40<00:00,  6.81s/it]
[2025-09-24T19:50:30.663Z] [STDERR] 2025-09-24 14:50:30,663 - hy3dgen.shapgen - INFO - Try to load model from local path: O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\hy3dgen\shapegen\models\Hunyuan3D\tencent/Hunyuan3D-2\hunyuan3d-dit-v2-0-turbo
[2025-09-24T19:50:30.673Z] [STDERR] 2025-09-24 14:50:30,673 - hy3dgen.shapgen - INFO - Loading model from O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\hy3dgen\shapegen\models\Hunyuan3D\tencent/Hunyuan3D-2\hunyuan3d-dit-v2-0-turbo\model.fp16.safetensors
[2025-09-24T19:51:48.598Z] [STDERR] 2025-09-24 14:51:48,597 - hy3dgen.shapgen - INFO - Try to load model from local path: O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\hy3dgen\shapegen\models\Hunyuan3D\tencent/Hunyuan3D-2\hunyuan3d-vae-v2-0-turbo
[2025-09-24T19:51:48.601Z] [STDERR] 2025-09-24 14:51:48,600 - hy3dgen.shapgen - INFO - Loading model from O:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\hy3dgen\shapegen\models\Hunyuan3D\tencent/Hunyuan3D-2\hunyuan3d-vae-v2-0-turbo\model.fp16.safetensors
[2025-09-24T19:51:55.399Z] [STDERR] INFO:     Started server process [23692]
INFO:     Waiting for application startup.
[2025-09-24T19:51:55.400Z] [STDERR] INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
[2025-09-24T19:51:55.486Z] [STDOUT] After launched, open a browser and enter 0.0.0.0:8080 into url, as if it was a website:
INFO:     127.0.0.1:55954 - "GET / HTTP/1.1" 200 OK
[2025-09-24T19:51:55.508Z] [STDOUT] INFO:     127.0.0.1:55954 - "POST /upload HTTP/1.1" 200 OK
[2025-09-24T19:52:01.196Z] [STDOUT] Created new folder: gradio_cache\5cf3612d-7983-4f6e-bc2b-320cc445534c
[2025-09-24T19:52:01.196Z] [STDERR] Diffusion Sampling::   0%|          | 0/25 [00:00<?, ?it/s]
[2025-09-24T19:52:04.057Z] [STDERR] Diffusion Sampling::  20%|##        | 5/25 [00:02<00:11,  1.77it/s]
[2025-09-24T19:52:06.972Z] [STDERR] Diffusion Sampling::  40%|####      | 10/25 [00:05<00:08,  1.72it/s]
[2025-09-24T19:52:09.880Z] [STDERR] Diffusion Sampling::  60%|######    | 15/25 [00:08<00:05,  1.72it/s]
[2025-09-24T19:52:12.806Z] [STDERR] Diffusion Sampling::  80%|########  | 20/25 [00:11<00:02,  1.71it/s]
[2025-09-24T19:52:15.743Z] [STDERR] Diffusion Sampling:: 100%|##########| 25/25 [00:14<00:00,  1.70it/s]
Diffusion Sampling:: 100%|##########| 25/25 [00:14<00:00,  1.72it/s]
[2025-09-24T19:52:15.835Z] [STDERR] 2025-09-24 14:52:15,833 - hy3dgen.shapgen - INFO - FlashVDMVolumeDecoding Resolution: [63, 126, 252]
[2025-09-24T19:52:16.256Z] [STDERR] FlashVDM Volume Decoding:   0%|          | 0/64 [00:00<?, ?it/s]
[2025-09-24T19:52:16.568Z] [STDERR] FlashVDM Volume Decoding: 100%|##########| 64/64 [00:00<00:00, 206.08it/s]
[2025-09-24T19:52:18.871Z] [STDERR] 2025-09-24 14:52:18,870 - hy3dgen.shapgen - INFO - ---Shape generation takes 20.74145770072937 seconds ---
[2025-09-24T19:52:19.411Z] [STDERR] 2025-09-24 14:52:19,410 - hy3dgen.shapgen - INFO - ---Postprocessing takes 0.4696187973022461 seconds ---
[2025-09-24T19:52:20.537Z] [STDERR] 2025-09-24 14:52:20,536 - hy3dgen.shapgen - INFO - ---Face Reduction takes 1.1255345344543457 seconds ---
[2025-09-24T19:53:36.895Z] [STDERR] 2025-09-24 14:53:36,895 - hy3dgen.shapgen - INFO - ---Texture Generation takes 76.35832953453064 seconds ---
[2025-09-24T19:53:38.752Z] [STDOUT] Find html file gradio_cache\5cf3612d-7983-4f6e-bc2b-320cc445534c\textured_mesh.html, True, relative HTML path is /static/5cf3612d-7983-4f6e-bc2b-320cc445534c\textured_mesh.html
INFO:     127.0.0.1:55955 - "POST /api/predict HTTP/1.1" 200 OK
[2025-09-24T19:53:38.766Z] [STDOUT] INFO:     127.0.0.1:55980 - "GET /file%3DC%3A/Users/<USER>/AppData/Local/Temp/gradio/6a0abc94af3994e982692ea8f5898eb90d28c0c65504a559fc1b34b49b0adcc3/textured_mesh.glb HTTP/1.1" 200 OK
[2025-09-24T19:53:44.577Z] [STDOUT] Something went wrong, consider removing code/hunyuan_init_done.txt and the venv folder to re-initialize from scratch
[2025-09-24T19:53:44.578Z] [STDOUT] Press any key to continue . . .
