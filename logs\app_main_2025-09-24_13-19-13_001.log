========================================
AIStudio Real-time Log: main
Started: 2025-09-24T18:19:13.345Z
File: app_main_2025-09-24_13-19-13_001.log
========================================

[2025-09-24T18:19:13.623Z] [INFO] AIStudio application started successfully
[2025-09-24T18:19:13.624Z] [INFO] [main] AIStudio application started successfully
[2025-09-24T18:19:13.672Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-24T18:19:14.749Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-24T18:19:42.844Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-24_13-19-42_001.log
[2025-09-24T18:19:42.844Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-24T18:19:45.327Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-09-24T18:19:45.328Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-09-24T18:19:46.334Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
